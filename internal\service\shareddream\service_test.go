package shareddream

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mock repositories
type MockShareLinkRepository struct {
	mock.Mock
}

func (m *MockShareLinkRepository) Find(ctx context.Context, id primitive.ObjectID) (*model.ShareLink, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.ShareLink), args.Error(1)
}

func (m *MockShareLinkRepository) FindByToken(ctx context.Context, token string) (*model.ShareLink, error) {
	args := m.Called(ctx, token)
	return args.Get(0).(*model.ShareLink), args.Error(1)
}

func (m *MockShareLinkRepository) FindByDreamID(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	args := m.Called(ctx, dreamID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.ShareLink), args.Error(1)
}

func (m *MockShareLinkRepository) Create(ctx context.Context, shareLink *model.ShareLink) (string, error) {
	args := m.Called(ctx, shareLink)
	return args.String(0), args.Error(1)
}

func (m *MockShareLinkRepository) Update(ctx context.Context, shareLink *model.ShareLink) error {
	args := m.Called(ctx, shareLink)
	return args.Error(0)
}

func (m *MockShareLinkRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockContributionRepository struct {
	mock.Mock
}

func (m *MockContributionRepository) Find(ctx context.Context, id primitive.ObjectID) (*model.Contribution, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.Contribution), args.Error(1)
}

func (m *MockContributionRepository) FindByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*model.Contribution), args.Error(1)
}

func (m *MockContributionRepository) FindByUserID(ctx context.Context, userID string) ([]*model.Contribution, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*model.Contribution), args.Error(1)
}

func (m *MockContributionRepository) FindByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error) {
	args := m.Called(ctx, dreamID, userID)
	return args.Get(0).(*model.Contribution), args.Error(1)
}

func (m *MockContributionRepository) FindActiveByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	args := m.Called(ctx, dreamID)
	return args.Get(0).([]*model.Contribution), args.Error(1)
}

func (m *MockContributionRepository) Create(ctx context.Context, contribution *model.Contribution) (string, error) {
	args := m.Called(ctx, contribution)
	return args.String(0), args.Error(1)
}

func (m *MockContributionRepository) Update(ctx context.Context, contribution *model.Contribution) error {
	args := m.Called(ctx, contribution)
	return args.Error(0)
}

func (m *MockContributionRepository) UpdateStatusByDreamID(ctx context.Context, dreamID string, status model.ContributionStatus) error {
	args := m.Called(ctx, dreamID, status)
	return args.Error(0)
}

func (m *MockContributionRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func TestCreateShareLink(t *testing.T) {
	mockShareLinkRepo := new(MockShareLinkRepository)
	mockContributionRepo := new(MockContributionRepository)
	service := New(mockShareLinkRepo, mockContributionRepo)

	ctx := context.Background()
	dreamID := "test-dream-id"

	// Mock that no existing share link exists
	notFoundError := errors.New(errors.Repository, "share link not found", errors.NotFound, nil)
	mockShareLinkRepo.On("FindByDreamID", ctx, dreamID).Return((*model.ShareLink)(nil), notFoundError)

	// Mock successful creation
	validObjectID := primitive.NewObjectID()
	mockShareLinkRepo.On("Create", ctx, mock.AnythingOfType("*model.ShareLink")).Return(validObjectID.Hex(), nil)

	// Mock successful find after creation
	expectedShareLink := &model.ShareLink{
		ObjectID:  validObjectID,
		ID:        validObjectID.Hex(),
		DreamID:   dreamID,
		Token:     "test-token",
		IsEnabled: true,
		ExpiresAt: time.Now().Add(7 * 24 * time.Hour),
	}
	mockShareLinkRepo.On("Find", ctx, validObjectID).Return(expectedShareLink, nil)

	shareLink, err := service.CreateShareLink(ctx, dreamID)

	assert.NoError(t, err)
	assert.NotNil(t, shareLink)
	assert.Equal(t, dreamID, shareLink.DreamID)
	assert.True(t, shareLink.IsEnabled)
	mockShareLinkRepo.AssertExpectations(t)
}

func TestCalculateDreamDuration(t *testing.T) {
	mockShareLinkRepo := new(MockShareLinkRepository)
	mockContributionRepo := new(MockContributionRepository)
	service := New(mockShareLinkRepo, mockContributionRepo)

	ctx := context.Background()
	dreamID := "test-dream-id"
	estimatedCost := monetary.Amount(1000)

	// Mock active contributions
	contributions := []*model.Contribution{
		{
			DreamID:              dreamID,
			ContributorUserID:    "user1",
			MonthlyPledgedAmount: monetary.Amount(100),
			Status:               model.ContributionStatusActive,
		},
		{
			DreamID:              dreamID,
			ContributorUserID:    "user2",
			MonthlyPledgedAmount: monetary.Amount(200),
			Status:               model.ContributionStatusActive,
		},
	}

	mockContributionRepo.On("FindActiveByDreamID", ctx, dreamID).Return(contributions, nil)

	duration, err := service.CalculateDreamDuration(ctx, dreamID, estimatedCost)

	assert.NoError(t, err)
	assert.NotNil(t, duration)
	assert.Equal(t, 3, *duration) // 1000 / (100 + 200) = 3.33 -> 3 months
	mockContributionRepo.AssertExpectations(t)
}

func TestCalculateDreamDurationWithZeroContributions(t *testing.T) {
	mockShareLinkRepo := new(MockShareLinkRepository)
	mockContributionRepo := new(MockContributionRepository)
	service := New(mockShareLinkRepo, mockContributionRepo)

	ctx := context.Background()
	dreamID := "test-dream-id"
	estimatedCost := monetary.Amount(1000)

	// Mock no active contributions
	contributions := []*model.Contribution{}
	mockContributionRepo.On("FindActiveByDreamID", ctx, dreamID).Return(contributions, nil)

	duration, err := service.CalculateDreamDuration(ctx, dreamID, estimatedCost)

	assert.NoError(t, err)
	assert.Nil(t, duration) // Cannot calculate duration with zero contributions
	mockContributionRepo.AssertExpectations(t)
}
