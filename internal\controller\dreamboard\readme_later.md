Clareza Imediata para o Current User:
O termo /me é amplamente reconhecido em APIs (ex.: GitHub, Spotify) para representar o recurso do usuário autenticado. Isso elimina a necessidade de expor ou adivinhar IDs na URL.

Exemplo de uso:

http
Copy
GET /v1/dreamboards/me → Retorna os dreamboards do usuário logado

Como o endpoint /me usa o ID do token JWT, você já garante que:

Usuários não-admins só acessam seus próprios dreamboards.

Admins podem acessar qualquer dreamboard via /v1/dreamboards?user_id=:id (caso necessário).

Ação do Usuário	Endpoint	Controller
"Quero ver MEUS dreamboards"	GET /v1/dreamboards/me	Dreamboard
Admin: "Ver dreamboards do João"	GET /v1/users/123/dreamboards	User (opcional)
Busca Global (Admin)	GET /v1/dreamboards?user_id=123	Dreamboard

Conclusão:
A opção /v1/dreamboards/me é a mais adequada para seu cenário pois:

Mantém o controller de Dreamboard como fonte única de verdade para operações com esse recurso.

Evita duplicação de código (não precisa replicar lógica no UserController).

É intuitivo para desenvolvedores que consomem a API.

