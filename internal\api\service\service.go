package service

import (
	"sync"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/repository"
	"github.com/dsoplabs/dinbora-backend/internal/cache" // Import cache package
	"github.com/dsoplabs/dinbora-backend/internal/service/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/service/auth"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/invoice"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/product"
	"github.com/dsoplabs/dinbora-backend/internal/service/billing/subscription"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/trophy"
	"github.com/dsoplabs/dinbora-backend/internal/service/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/league" // Added league service import
	"github.com/dsoplabs/dinbora-backend/internal/service/notification/sendgrid"
	"github.com/dsoplabs/dinbora-backend/internal/service/progression"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/shareddream"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripecustomer"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripeinvoice"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripeprice"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripeproduct"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/striperefund"
	"github.com/dsoplabs/dinbora-backend/internal/service/stripe/stripewebhook"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/dsoplabs/dinbora-backend/internal/service/vault"
	"github.com/dsoplabs/dinbora-backend/internal/service/webhook"
	_sendgrid "github.com/sendgrid/sendgrid-go"
)

type ServiceRegistry struct {
	Auth auth.Service

	// Billing
	Contract     contract.Service
	Invoice      invoice.Service
	Product      product.Service
	Subscription subscription.Service

	// Content
	InvestmentCategory investmentcategory.Service
	Ticker             ticker.Service
	Trail              trail.Service
	Trophy             trophy.Service
	Wallet             wallet.Service

	Dreamboard dreamboard.Service
	//Facebook facebook.Service
	FinancialDNA   financialdna.Service
	FinancialSheet financialsheet.Service
	League         league.Service // Added League service
	//Google   google.Service
	Progression progression.Service

	// Shared Dreams
	SharedDream shareddream.Service
	Sendgrid    sendgrid.Service

	// Stripe
	StripeCustomer stripecustomer.Service
	StripeInvoice  stripeinvoice.Service
	StripePrice    stripeprice.Service
	StripeProduct  stripeproduct.Service
	StripeRefund   striperefund.Service
	StripeWebhook  stripewebhook.Service

	// Storage
	S3 s3.Service

	// AI
	AIAssistant aiassistant.Service

	User    user.Service
	Vault   vault.Service
	Webhook webhook.Service
}

type ServiceContainer struct {
	services       map[string]interface{}
	repos          *repository.RepositoryRegistry
	mu             sync.RWMutex
	sendgridClient *_sendgrid.Client
}

func NewContainer(repos *repository.RepositoryRegistry, sendgridClient *_sendgrid.Client) *ServiceContainer {
	return &ServiceContainer{
		services:       make(map[string]interface{}),
		repos:          repos,
		sendgridClient: sendgridClient,
	}
}

func (sc *ServiceContainer) Register(name string, service interface{}) {
	sc.mu.Lock()
	defer sc.mu.Unlock()
	sc.services[name] = service
}

func (sc *ServiceContainer) Get(name string) interface{} {
	sc.mu.RLock()
	defer sc.mu.RUnlock()
	return sc.services[name]
}

func (sc *ServiceContainer) Initialize() *ServiceRegistry {
	// Lazy initialization of Services
	lazyInitService := func(name string, initFunc func() interface{}) interface{} {
		if existing := sc.Get(name); existing != nil {
			return existing
		}
		service := initFunc()
		sc.Register(name, service)
		return service
	}

	// Initialize Cache Service
	// Use reasonable defaults: 1 hour default expiration, 10 minutes cleanup interval
	cacheService := cache.NewInMemoryCache(1*time.Hour, 10*time.Minute)

	// Single dependency Services
	// Content
	investmentCategoryService := lazyInitService("investmentcategory", func() interface{} {
		return investmentcategory.New(sc.repos.Investimentcategory)
	}).(investmentcategory.Service)
	tickerService := lazyInitService("ticker", func() interface{} {
		return ticker.New(sc.repos.Ticker)
	}).(ticker.Service)
	trailService := lazyInitService("trail", func() interface{} {
		// Pass the initialized cache service to trail.New
		return trail.New(sc.repos.Trail, cacheService)
	}).(trail.Service)
	trophyService := lazyInitService("trophy", func() interface{} {
		return trophy.New(sc.repos.Trophy)
	}).(trophy.Service)

	// Notification
	sendgridService := lazyInitService("sendgrid", func() interface{} {
		return sendgrid.New(sc.sendgridClient)
	}).(sendgrid.Service)

	// Stripe
	stripeCustomerService := lazyInitService("stripecustomer", func() interface{} {
		return stripecustomer.New(sc.repos.StripeCustomer)
	}).(stripecustomer.Service)
	stripeInvoiceService := lazyInitService("stripeinvoice", func() interface{} {
		return stripeinvoice.New(sc.repos.StripeInvoice)
	}).(stripeinvoice.Service)
	stripePriceService := lazyInitService("stripeprice", func() interface{} {
		return stripeprice.New(sc.repos.StripePrice)
	}).(stripeprice.Service)
	stripeProductService := lazyInitService("stripeproduct", func() interface{} {
		return stripeproduct.New(sc.repos.StripeProduct)
	}).(stripeproduct.Service)
	stripeRefundService := lazyInitService("striperefund", func() interface{} {
		return striperefund.New(sc.repos.StripeRefund)
	}).(striperefund.Service)
	stripeWebhookService := lazyInitService("stripewebhook", func() interface{} {
		return stripewebhook.New(sc.repos.StripeWebhook)
	}).(stripewebhook.Service)

	// Storage
	s3Service := lazyInitService("s3", func() interface{} {
		return s3.New()
	}).(s3.Service)

	vaultService := lazyInitService("vault", func() interface{} {
		return vault.New(sc.repos.Vault)
	}).(vault.Service)

	// Multi dependency Services
	// Billing
	invoiceService := lazyInitService("invoice", func() interface{} {
		return invoice.New(sc.repos.Invoice, stripeInvoiceService)
	}).(invoice.Service)
	productService := lazyInitService("product", func() interface{} {
		return product.New(sc.repos.Product, stripePriceService, stripeProductService)
	}).(product.Service)
	contractService := lazyInitService("contract", func() interface{} {
		return contract.New(sc.repos.Contract, productService, invoiceService)
	}).(contract.Service)

	// Content
	walletService := lazyInitService("wallet", func() interface{} {
		return wallet.New(sc.repos.Wallet, contractService, productService, tickerService)
	}).(wallet.Service)

	// Feature Services
	// Initialize SharedDream service first as it's needed by dreamboard
	sharedDreamService := lazyInitService("shareddream", func() interface{} {
		return shareddream.New(sc.repos.ShareLink, sc.repos.Contribution)
	}).(shareddream.Service)

	dreamboardService := lazyInitService("dreamboard", func() interface{} {
		return dreamboard.New(sc.repos.Dreamboard, sc.repos.FinancialSheet, sharedDreamService)
	}).(dreamboard.Service)

	leagueService := lazyInitService("league", func() interface{} {
		return league.New(sc.repos.League) // Corrected League service constructor
	}).(league.Service)

	financialSheetService := lazyInitService("financialsheet", func() interface{} {
		return financialsheet.New(sc.repos.FinancialSheet, sc.repos.Dreamboard, leagueService) // Pass leagueService
	}).(financialsheet.Service)
	financialDNAService := lazyInitService("financialdna", func() interface{} {
		return financialdna.New(sc.repos.FinancialDNA, s3Service)
	}).(financialdna.Service)

	progressionService := lazyInitService("progression", func() interface{} {
		// Pass cacheService to progression.New
		return progression.New(sc.repos.Progression, trailService, trophyService, vaultService, contractService, cacheService)
	}).(progression.Service)

	userService := lazyInitService("user", func() interface{} {
		return user.New(sc.repos.User, dreamboardService, financialDNAService, financialSheetService, progressionService, vaultService, contractService, walletService, stripeCustomerService)
	}).(user.Service)

	subscriptionService := lazyInitService("subscription", func() interface{} {
		return subscription.New(sc.repos.StripeSubscription, userService, contractService, productService, invoiceService, stripeRefundService, stripeInvoiceService, sendgridService)
	}).(subscription.Service)

	// Authorization Services
	authService := lazyInitService("auth", func() interface{} {
		return auth.New(userService, sendgridService, s3Service)
	}).(auth.Service)

	webhookService := lazyInitService("webhook", func() interface{} {
		return webhook.New(sc.repos.Webhook, stripeWebhookService)
	}).(webhook.Service)

	// AI Assistant Service
	aiAssistantService := lazyInitService("aiassistant", func() interface{} {
		return aiassistant.New(
			userService,
			dreamboardService,
			financialDNAService,
			financialSheetService,
			progressionService,
			vaultService,
			contractService,
			walletService,
			trophyService,
			trailService,
		)
	}).(aiassistant.Service)

	return &ServiceRegistry{
		Auth:               authService,
		Contract:           contractService,
		Invoice:            invoiceService,
		Product:            productService,
		Subscription:       subscriptionService,
		InvestmentCategory: investmentCategoryService,
		Ticker:             tickerService,
		Trail:              trailService,
		Trophy:             trophyService,
		Wallet:             walletService,
		Dreamboard:         dreamboardService,
		//Facebook:           facebookService,
		FinancialDNA:   financialDNAService,
		FinancialSheet: financialSheetService,
		League:         leagueService, // Added League service to registry
		//Google:             googleService,
		Sendgrid:       sendgridService,
		Progression:    progressionService,
		SharedDream:    sharedDreamService,
		StripeCustomer: stripeCustomerService,
		StripeInvoice:  stripeInvoiceService,
		StripePrice:    stripePriceService,
		StripeProduct:  stripeProductService,
		StripeRefund:   stripeRefundService,
		StripeWebhook:  stripeWebhookService,
		S3:             s3Service,
		AIAssistant:    aiAssistantService, // Added AIAssistant service to registry
		User:           userService,
		Vault:          vaultService,
		Webhook:        webhookService,
	}
}

// Implementation of a Service using the container approach, evaluate in the future if we want to have dynamic service/controller/repository allocation.
// investmentCategoryService := lazyInitService("investmentcategory", func() interface{} {
// 	investmentCategoryRepo := sc.repos.Get("investmentcategory").(_investmentcategory.Repository)
// 	return investmentcategory.New(investmentCategoryRepo)
// }).(investmentcategory.Service)
