package financialsheet

import (
	"context"
	"fmt"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	_financialsheet "github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/service/league" // Added league service import
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Find(ctx context.Context, id string) (*financialsheet.Record, error)
	FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error)
	FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool) (*financialsheet.Record, error)
	Update(ctx context.Context, record *financialsheet.Record) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error)
	FindCategory(ctx context.Context, id string) (*financialsheet.Category, error)
	FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error)
	FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error)
	UpdateCategory(ctx context.Context, category *financialsheet.Category) error
	DeleteCategory(ctx context.Context, id string, userID string) error

	// Transaction CRUD
	CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error)
	FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error)
	FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType) ([]*financialsheet.Transaction, error)
	UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error)
	DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error)

	// League System Methods - These are now handled by the league service
	// ManageLeagueProgression(ctx context.Context, userID string) error
	// GetCurrentLeagueStatus(ctx context.Context, userID string) (*financialsheet.League, error)
	// StartNewSeason(ctx context.Context, userID string) error
	// GetLeagueRanking(ctx context.Context) (*financialsheet.LeagueRanking, error)

	// Utility
	Initialize(ctx context.Context, userID string, userName string) error
	CountUserCategories(ctx context.Context, userID string) (int, error)
}

type service struct {
	Repository           _financialsheet.Repository
	DreamboardRepository dreamboard.Repository
	LeagueService        league.Service // Added league service
}

func New(repository _financialsheet.Repository, dreamboardRepository dreamboard.Repository, leagueService league.Service) Service {
	return &service{
		Repository:           repository,
		DreamboardRepository: dreamboardRepository,
		LeagueService:        leagueService, // Initialize league service
	}
}

// CRUD
func (s *service) Find(ctx context.Context, id string) (*financialsheet.Record, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}

	record, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	if record != nil && !record.ObjectID.IsZero() {
		record.ID = record.ObjectID.Hex()
	}
	return record, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	record, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	if record != nil && !record.ObjectID.IsZero() {
		record.ID = record.ObjectID.Hex()
	}

	// Restore all transaction IDs
	for year := range record.YearData {
		for month := range record.YearData[year] {
			for i := range record.YearData[year][month].Transactions {
				record.YearData[year][month].Transactions[i].ID = record.YearData[year][month].Transactions[i].ObjectID.Hex()
			}
		}
	}

	return record, nil
}

// FindByUserAndPeriod retrieves a user's financial record filtered by year and month
func (s *service) FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool) (*financialsheet.Record, error) {
	record, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// If no period specified at all, return full record
	if year == 0 && month == 0 {
		return record, nil
	}

	// Create filtered record
	filteredRecord := &financialsheet.Record{
		ID:       record.ID,
		ObjectID: record.ObjectID,
		UserID:   record.UserID,
		UserName: record.UserName,
		// League:   record.League, // League removed
		Points:   record.Points,
		YearData: make(map[int]financialsheet.YearData),
	}

	// If only year specified
	if year > 0 && month == 0 {
		if yearData, exists := record.YearData[year]; exists {
			filteredRecord.YearData[year] = yearData
		}
		// Calculate totals for the year
		if yearData, ok := record.YearData[year]; ok {
			for _, monthData := range yearData {
				for _, card := range monthData.Categories {
					category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
					if err != nil {
						return nil, err
					}
					switch category.Type {
					case financialsheet.CategoryTypeIncome:
						filteredRecord.TotalIncome += card.Value
					case financialsheet.CategoryTypeCostsOfLiving:
						filteredRecord.TotalCostsOfLiving += card.Value
					case financialsheet.CategoryTypeExpense:
						filteredRecord.TotalExpenses += card.Value
					}
				}
			}
		}
		filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
		return filteredRecord, nil
	}

	// If only month specified
	if year == 0 && month > 0 {
		monthStr := fmt.Sprintf("%02d", month)
		for year, yearData := range record.YearData {
			if monthData, exists := yearData[monthStr]; exists {
				filteredRecord.YearData[year] = make(financialsheet.YearData)
				filteredRecord.YearData[year][monthStr] = monthData

				// Calculate totals
				for _, card := range monthData.Categories {
					category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
					if err != nil {
						return nil, err
					}
					switch category.Type {
					case financialsheet.CategoryTypeIncome:
						filteredRecord.TotalIncome += card.Value
					case financialsheet.CategoryTypeCostsOfLiving:
						filteredRecord.TotalCostsOfLiving += card.Value
					case financialsheet.CategoryTypeExpense:
						filteredRecord.TotalExpenses += card.Value
					}
				}
			}
		}
		filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
		return filteredRecord, nil
	}

	// Get user categories
	userCategories, err := s.FindAllCategoriesByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Create filtered record
	filteredRecord = &financialsheet.Record{
		ID:       record.ID,
		ObjectID: record.ObjectID,
		UserID:   record.UserID,
		UserName: record.UserName,
		// League:   record.League, // League removed
		Points:   record.Points,
		YearData: make(map[int]financialsheet.YearData),
	}

	// Initialize year and month
	filteredRecord.YearData[year] = make(financialsheet.YearData)
	monthStr := fmt.Sprintf("%02d", month)
	monthData := financialsheet.MonthData{
		Categories: make([]financialsheet.CategoryCard, 0, len(userCategories)),
	}

	// Get existing data for the month if it exists
	if existingYearData, ok := record.YearData[year]; ok {
		if existingMonthData, ok := existingYearData[monthStr]; ok {
			// Create a map of existing category cards
			categoryCardsMap := make(map[string]financialsheet.CategoryCard)
			for _, card := range existingMonthData.Categories {
				categoryCardsMap[card.Identifier] = card
				// Calculate totals based on category type
				category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}

				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					filteredRecord.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					filteredRecord.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					filteredRecord.TotalExpenses += card.Value
				}
			}

			// Get all predefined categories to maintain order
			allCategories := financialsheet.GetAllCategories()

			// Build categories slice in predefined order
			for _, cat := range allCategories {
				identifier := string(cat.Identifier)
				if card, exists := categoryCardsMap[identifier]; exists {
					// Update category card Icon and Background for the already created ones
					card.Type = cat.Type
					card.Icon = cat.Icon
					card.Background = cat.Background
					monthData.Categories = append(monthData.Categories, card)
				} else {
					// Add missing category with zero value
					monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
						ID:         cat.ID,
						Identifier: identifier,
						Type:       cat.Type,
						Name:       cat.Name,
						Icon:       cat.Icon,
						Background: cat.Background,
						Value:      0,
					})
				}
			}

			// Add user-created categories
			for _, cat := range userCategories {
				// Skip predefined categories as they're already added
				if cat.User == "" {
					continue
				}

				identifier := string(cat.Identifier)
				if card, exists := categoryCardsMap[identifier]; exists {
					// Update category card Icon and Background for the already created ones
					card.Type = cat.Type
					card.Icon = cat.Icon
					card.Background = cat.Background
					monthData.Categories = append(monthData.Categories, card)
				} else {
					// Add missing user category with zero value
					monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
						ID:         cat.ID,
						Identifier: identifier,
						Type:       cat.Type,
						Name:       cat.Name,
						Icon:       cat.Icon,
						Background: cat.Background,
						Value:      0,
					})
				}
			}
			monthData.Transactions = existingMonthData.Transactions
		} else {
			// Month exists but no data, add all categories with zero values
			allCategories := financialsheet.GetAllCategories()
			for _, cat := range allCategories {
				monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
					ID:         cat.ID,
					Identifier: string(cat.Identifier),
					Type:       cat.Type,
					Name:       cat.Name,
					Icon:       cat.Icon,
					Background: cat.Background,
					Value:      0,
				})
			}

			// Add user-created categories with zero values
			for _, cat := range userCategories {
				// Skip predefined categories as they're already added
				if cat.User == "" {
					continue
				}

				monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
					ID:         cat.ID,
					Identifier: string(cat.Identifier),
					Type:       cat.Type,
					Name:       cat.Name,
					Icon:       cat.Icon,
					Background: cat.Background,
					Value:      0,
				})
			}
		}
	} else {
		// Year doesn't exist, add all categories with zero values
		allCategories := financialsheet.GetAllCategories()
		for _, cat := range allCategories {
			monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
				ID:         cat.ID,
				Identifier: string(cat.Identifier),
				Type:       cat.Type,
				Name:       cat.Name,
				Icon:       cat.Icon,
				Background: cat.Background,
				Value:      0,
			})
		}

		// Add user-created categories with zero values
		for _, cat := range userCategories {
			// Skip predefined categories as they're already added
			if cat.User == "" {
				continue
			}

			monthData.Categories = append(monthData.Categories, financialsheet.CategoryCard{
				ID:         cat.ID,
				Identifier: string(cat.Identifier),
				Type:       cat.Type,
				Name:       cat.Name,
				Icon:       cat.Icon,
				Background: cat.Background,
				Value:      0,
			})
		}
	}

	// Set the categories and calculate balance
	if flatten {
		// Copy categories from month data and remove yearData
		filteredRecord.Categories = monthData.Categories
		filteredRecord.YearData = nil
	} else {
		// Set the month data in yearData structure
		filteredRecord.YearData[year][monthStr] = monthData
	}

	filteredRecord.Balance = filteredRecord.TotalIncome - (filteredRecord.TotalCostsOfLiving + filteredRecord.TotalExpenses)
	return filteredRecord, nil
}

func (s *service) CountUserCategories(ctx context.Context, userID string) (int, error) {
	categories, err := s.Repository.FindAllCategories(ctx)
	if err != nil {
		return 0, err
	}

	count := 0
	for _, cat := range categories {
		if cat.User == userID {
			count++
		}
	}
	return count, nil
}

func (s *service) Update(ctx context.Context, record *financialsheet.Record) error {
	if record.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(record.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid financial record ID", errors.Validation, err)
		}
		record.ObjectID = objID
	}

	return s.Repository.Update(ctx, record)
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}
	return s.Repository.Delete(ctx, objID)
}

// Category CRUD
func (s *service) CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	if err := category.Identifier.Validate(); err != nil {
		return nil, errors.New(errors.Service, "invalid category identifier", errors.Validation, err)
	}
	// Fix Identifier
	numbersOfUserCategories, err := s.CountUserCategories(ctx, userID)
	if err != nil {
		return nil, err
	}
	identifierStr := fmt.Sprintf("%s_%d", category.Identifier, (numbersOfUserCategories + 1))
	// Set type icon
	if category.Type == financialsheet.CategoryTypeIncome {
		category.Icon = financialsheet.CategoryIconNewCategoryIncome
		category.Background = financialsheet.CategoryBackgroundIncome
	} else if category.Type == financialsheet.CategoryTypeExpense {
		category.Icon = financialsheet.CategoryIconNewCategoryExpense
		category.Background = financialsheet.CategoryBackgroundExpense
	}

	newCategory := &financialsheet.Category{
		User:       userID,
		Identifier: financialsheet.CategoryIdentifier(identifierStr),
		Type:       category.Type,
		Name:       category.Name,
		Icon:       category.Icon,
		Background: category.Background,
		MoneySource: []financialsheet.MoneySource{
			financialsheet.MoneySourceOpt1,
		},
		PaymentMethod: []financialsheet.PaymentMethod{
			financialsheet.PaymentMethodOpt1,
			financialsheet.PaymentMethodOpt2,
			financialsheet.PaymentMethodOpt3,
			financialsheet.PaymentMethodOpt4,
			financialsheet.PaymentMethodOpt5,
			financialsheet.PaymentMethodOther,
		},
	}

	categoryID, err := s.Repository.CreateCategory(ctx, newCategory)
	if err != nil {
		return nil, err
	}

	newCategory.ID = categoryID
	return newCategory, nil
}

func (s *service) FindCategory(ctx context.Context, id string) (*financialsheet.Category, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}

	return s.Repository.FindCategory(ctx, objID)
}

func (s *service) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	return s.Repository.FindAllCategories(ctx)
}

func (s *service) FindAllCategoriesByUser(ctx context.Context, userID string) ([]*financialsheet.Category, error) {
	categories, err := s.Repository.FindAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	userCategories := []*financialsheet.Category{}
	for _, category := range categories {
		if category.User == userID || category.User == "" {
			userCategories = append(userCategories, category)
		}
	}
	return userCategories, nil
}

func (s *service) FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	return s.Repository.FindCategoryByIdentifier(ctx, identifier)
}

func (s *service) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	if err := category.Identifier.Validate(); err != nil {
		return errors.New(errors.Service, "invalid category identifier", errors.Validation, err)
	}

	objID, err := primitive.ObjectIDFromHex(category.ID)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}
	category.ObjectID = objID

	return s.Repository.UpdateCategory(ctx, category)
}

func (s *service) DeleteCategory(ctx context.Context, id string, userID string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.Validation, err)
	}

	// First, find the category to validate ownership and type
	category, err := s.Repository.FindCategory(ctx, objID)
	if err != nil {
		return err
	}

	// Check if it's a system category (empty user field)
	if category.User == "" {
		return errors.New(errors.Service, "cannot delete system categories", errors.Forbidden, nil)
	}

	// Check if the category belongs to the requesting user
	if category.User != userID {
		return errors.New(errors.Service, "you can only delete your own categories", errors.Forbidden, nil)
	}

	// Check if the category is being used in any transactions
	transactions, err := s.Repository.FindAllTransactions(ctx, userID, "")
	if err != nil {
		return err
	}

	for _, transaction := range transactions {
		if string(transaction.Category) == string(category.Identifier) {
			return errors.New(errors.Service, "cannot delete category that is being used in transactions", errors.Conflict, nil)
		}
	}

	return s.Repository.DeleteCategory(ctx, objID)
}

// Transaction CRUD
func (s *service) CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	year := transaction.Date.Year()
	month := transaction.Date.Format("01")

	// Update streak information
	if record.Points.LastTransactionDate.IsZero() {
		record.Points.Current = 1
		record.Points.Best = record.Points.Current
	} else {
		daysSinceLastTransaction := int(transaction.Date.Sub(record.Points.LastTransactionDate).Hours() / 24)
		if daysSinceLastTransaction == 1 {
			record.Points.Current++
			if record.Points.Current > record.Points.Best {
				record.Points.Best = record.Points.Current
			}
		} else if daysSinceLastTransaction > 1 {
			// Reset streak if more than one day has passed
			record.Points.Current = 1
			record.Points.MissedDays = append(record.Points.MissedDays, record.Points.LastTransactionDate.AddDate(0, 0, 1))
			// record.League.CurrentLevel = financialsheet.BronzeLevel // Old league logic removed
		}
	}

	record.Points.LastTransactionDate = transaction.Date

	// Update league tier based on new streak - OLD LOGIC REMOVED
	// switch {
	// case record.Points.Current >= 90:
	// record.League.CurrentLevel = financialsheet.DiamondLevel
	// case record.Points.Current >= 60:
	// record.League.CurrentLevel = financialsheet.GoldLevel
	// case record.Points.Current >= 30:
	// record.League.CurrentLevel = financialsheet.SilverLevel
	// default:
	// record.League.CurrentLevel = financialsheet.BronzeLevel
	// }

	// Initialize year and month data if they don't exist
	if record.YearData == nil {
		record.YearData = make(map[int]financialsheet.YearData)
	}
	if _, ok := record.YearData[year]; !ok {
		record.YearData[year] = make(financialsheet.YearData)
	}

	// Initialize month data if it doesn't exist
	if _, ok := record.YearData[year][month]; !ok {
		record.YearData[year][month] = financialsheet.MonthData{
			Categories: make([]financialsheet.CategoryCard, 0),
		}
	}

	// Get category first to validate and use its information
	category, err := s.FindCategoryByIdentifier(ctx, transaction.Category)
	if err != nil {
		return nil, err
	}

	// Validate transaction type matches category type
	if category.Type != transaction.Type {
		return nil, errors.New(errors.Service, "transaction type does not match category type", errors.Validation, nil)
	}

	// Generate unique transaction ID
	transaction.ObjectID = primitive.NewObjectID()
	transaction.ID = transaction.ObjectID.Hex()

	// Get the current month's data and prepare updates
	monthData := record.YearData[year][month]

	// Update category summary
	var categoryFound bool
	var updatedCategories []financialsheet.CategoryCard

	// Update existing category summary or create new one
	for _, card := range monthData.Categories {
		newCard := card
		if card.Identifier == string(category.Identifier) {
			newCard.Value += transaction.Value
			categoryFound = true
		}
		updatedCategories = append(updatedCategories, newCard)
	}

	// Add new category summary if not found
	if !categoryFound {
		updatedCategories = append(updatedCategories, financialsheet.CategoryCard{
			ID:         category.ID,
			Identifier: string(category.Identifier),
			Name:       category.Name,
			Value:      transaction.Value,
		})
	}

	// Create new month data with updated categories and new transaction
	newMonthData := financialsheet.MonthData{
		Categories:   updatedCategories,
		Transactions: append(monthData.Transactions, *transaction),
	}

	// Update the month data in the record
	record.YearData[year][month] = newMonthData

	// Recalculate totals from all transactions to ensure consistency
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in the database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	// After successful financial record update, record investida for leagues
	if s.LeagueService != nil {
		if errLs := s.LeagueService.RecordTransactionForAllUserLeagues(ctx, record.UserID, transaction.Date); errLs != nil {
			// Log the error but do not fail the transaction creation itself,
			// as the financial record is already saved.
			// Consider a more robust error handling strategy for production.
			fmt.Printf("Warning: failed to record investida for user %s in leagues: %v\n", record.UserID, errLs) // Placeholder for proper logging
		}
	}

	return record, nil
}

// FindTransaction finds a transaction in a user's financial record
func (s *service) FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error) {
	// Convert recordID to ObjectID
	recordObjID, err := primitive.ObjectIDFromHex(recordID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid financial record ID", errors.BadRequest, err)
	}

	// Get the financial record
	record, err := s.Repository.Find(ctx, recordObjID)
	if err != nil {
		return nil, err
	}

	// Attempt to convert transactionID to ObjectID for validation
	if _, err := primitive.ObjectIDFromHex(transactionID); err != nil {
		return nil, errors.New(errors.Service, "invalid transaction ID", errors.BadRequest, err)
	}

	// Search for the transaction in the record's transactions
	for year := range record.YearData {
		for month := range record.YearData[year] {
			monthData := record.YearData[year][month]
			for i := range monthData.Transactions {
				if monthData.Transactions[i].ObjectID.Hex() == transactionID {
					// Return a copy to avoid any pointer issues
					found := monthData.Transactions[i]
					return &found, nil
				}
			}
		}
	}

	return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
}

// FindAllTransactions retrieves all transactions for a user with optional category filtering
func (s *service) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType) ([]*financialsheet.Transaction, error) {
	// Validate category type if provided
	if categoryType != "" {
		if err := categoryType.Validate(); err != nil {
			return nil, errors.New(errors.Service, "invalid category type", errors.Validation, err)
		}
	}

	// Call repository method
	return s.Repository.FindAllTransactions(ctx, userID, categoryType)
}

// UpdateTransaction updates an existing transaction
func (s *service) UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	if record == nil {
		return nil, errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	if transaction == nil {
		return nil, errors.New(errors.Service, "invalid transaction", errors.BadRequest, nil)
	}

	// Validate transaction type matches category type
	category, err := s.FindCategoryByIdentifier(ctx, transaction.Category)
	if err != nil {
		return nil, err
	}
	if category.Type != transaction.Type {
		return nil, errors.New(errors.Service, "transaction type does not match category type", errors.Validation, nil)
	}

	year := transaction.Date.Year()
	month := transaction.Date.Format("01")

	// Initialize year data if it doesn't exist
	if record.YearData == nil {
		record.YearData = make(map[int]financialsheet.YearData)
	}
	if _, ok := record.YearData[year]; !ok {
		record.YearData[year] = make(financialsheet.YearData)
	}

	var found bool
	var oldTransaction *financialsheet.Transaction

	// Find existing transaction and get its old value
	for searchYear := range record.YearData {
		for searchMonth := range record.YearData[searchYear] {
			for i, t := range record.YearData[searchYear][searchMonth].Transactions {
				if t.ObjectID.Hex() == transaction.ID {
					oldTransaction = &record.YearData[searchYear][searchMonth].Transactions[i]
					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	// Get month data for the target month (where we want to update the transaction)
	monthData := record.YearData[year][month]
	var updatedCategories []financialsheet.CategoryCard
	var updatedTransactions []financialsheet.Transaction

	// Update transactions
	for _, t := range monthData.Transactions {
		if t.ObjectID.Hex() == transaction.ID {
			updatedTransactions = append(updatedTransactions, *transaction)
		} else {
			updatedTransactions = append(updatedTransactions, t)
		}
	}

	// Update category summaries
	categoryMap := make(map[string]financialsheet.CategoryCard)
	for _, card := range monthData.Categories {
		categoryMap[card.Identifier] = card
	}

	// Remove old transaction value from old category
	if oldCard, exists := categoryMap[string(oldTransaction.Category)]; exists {
		oldCard.Value -= oldTransaction.Value
		categoryMap[string(oldTransaction.Category)] = oldCard
	}

	// Add new transaction value to new category
	if newCard, exists := categoryMap[string(transaction.Category)]; exists {
		newCard.Value += transaction.Value
		categoryMap[string(transaction.Category)] = newCard
	} else {
		categoryMap[string(transaction.Category)] = financialsheet.CategoryCard{
			ID:         category.ID,
			Identifier: string(transaction.Category),
			Name:       category.Name,
			Value:      transaction.Value,
		}
	}

	// Convert map back to slice
	for _, card := range categoryMap {
		if card.Value != 0 {
			updatedCategories = append(updatedCategories, card)
		}
	}

	// Create new month data with updated categories and transactions
	newMonthData := financialsheet.MonthData{
		Categories:   updatedCategories,
		Transactions: updatedTransactions,
	}
	record.YearData[year][month] = newMonthData

	// Recalculate totals
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	return record, nil
}

// DeleteTransaction removes a transaction from the record
func (s *service) DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	if record == nil {
		return nil, errors.New(errors.Service, "invalid record", errors.BadRequest, nil)
	}

	var found bool
	var foundTransaction *financialsheet.Transaction

	// Find the transaction to remove
	for year := range record.YearData {
		for month := range record.YearData[year] {
			monthData := record.YearData[year][month]
			for i, transaction := range monthData.Transactions {
				if transaction.ObjectID.Hex() == transactionID {
					foundTransaction = &monthData.Transactions[i]

					// Update category summary
					var updatedCategories []financialsheet.CategoryCard
					for _, card := range monthData.Categories {
						newCard := card
						if card.Identifier == string(foundTransaction.Category) {
							// Subtract the transaction value from category summary
							newCard.Value -= foundTransaction.Value
						}
						updatedCategories = append(updatedCategories, newCard)
					}

					// Create new transactions slice without the removed transaction
					updatedTransactions := append(
						monthData.Transactions[:i],
						monthData.Transactions[i+1:]...,
					)

					// Update the month data
					record.YearData[year][month] = financialsheet.MonthData{
						Categories:   updatedCategories,
						Transactions: updatedTransactions,
					}

					found = true
					break
				}
			}
			if found {
				break
			}
		}
		if found {
			break
		}
	}

	if !found {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	if !found || foundTransaction == nil {
		return nil, errors.New(errors.Service, "transaction not found", errors.NotFound, nil)
	}

	// Recalculate totals from all transactions
	record.TotalIncome = 0
	record.TotalCostsOfLiving = 0
	record.TotalExpenses = 0

	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for _, card := range monthData.Categories {
				category, err := s.FindCategoryByIdentifier(ctx, financialsheet.CategoryIdentifier(card.Identifier))
				if err != nil {
					return nil, err
				}
				switch category.Type {
				case financialsheet.CategoryTypeIncome:
					record.TotalIncome += card.Value
				case financialsheet.CategoryTypeCostsOfLiving:
					record.TotalCostsOfLiving += card.Value
				case financialsheet.CategoryTypeExpense:
					record.TotalExpenses += card.Value
				}
			}
		}
	}

	record.Balance = record.TotalIncome - (record.TotalCostsOfLiving + record.TotalExpenses)

	// Update record in database
	if err := s.Repository.Update(ctx, record); err != nil {
		return nil, err
	}

	return record, nil
}

// League System Methods - Implementations removed as they are now part of league.Service

// Utility
func (s *service) Initialize(ctx context.Context, userID string, userName string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		// Only proceed if the error is NotFound, otherwise return the error
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			return err
		}
		// For NotFound errors, we continue to create a new record
	} else if existing != nil {
		// If we found an existing record, return Conflict
		return errors.New(errors.Service, "financial record already exists", errors.Conflict, nil)
	}

	// currentTime := time.Now() // Unused variable
	newRecord := &financialsheet.Record{
		UserID:   userID,
		UserName: userName,
		Balance:  monetary.Amount(0),
		YearData: make(map[int]financialsheet.YearData),
		// League field removed from financialsheet.Record model
	}

	_, err = s.Repository.Create(ctx, newRecord)
	return err
}
