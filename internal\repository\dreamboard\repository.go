package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.Dreamboard, error)
	FindAll(ctx context.Context) ([]*model.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error)

	// Category Management
	FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*model.Category, error)
	// Dream Management
	FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*model.Dream, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error)
	CreateDelete(ctx context.Context, deletedDreamboard *model.DeletedDreamboard) error

	// Update operations
	Update(ctx context.Context, dreamboard *model.Dreamboard) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Category Management
	CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error
	CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []model.Category) error
	UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error
	DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error
	// Dream Management
	CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error
	UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error
	RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
