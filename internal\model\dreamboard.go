package model

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Dreamboard struct {
	ObjectID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID              string             `json:"id,omitempty" bson:"-"`
	User            string             `json:"user" bson:"user"`
	Categories      []*Category        `json:"categories" bson:"categories"`
	Dreams          []*Dream           `json:"dreams" bson:"dreams"`
	TotalDreamsCost monetary.Amount    `json:"totalDreamsCost" bson:"savedAmount"`
	SavedAmount     monetary.Amount    `json:"savedAmount"`
	MonthlyNeeded   monetary.Amount    `json:"monthlyNeeded" bson:"monthlyNeeded"`
	RemainingAmount monetary.Amount    `json:"remainingAmount" bson:"remainingAmount"`
	CreatedAt       time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time          `json:"updatedAt" bson:"updatedAt"`
}

func (d *Dreamboard) New() {
	d.CreatedAt = time.Now()
	d.UpdatedAt = time.Now()
}

func (d *Dreamboard) Validate() error {
	if d.User == "" {
		return errors.New(errors.Model, "user ID must be specified", errors.Validation, nil)
	}
	if d.Dreams == nil {
		return errors.New(errors.Model, "dreams is invalid", errors.Validation, nil)
	}
	if d.Categories == nil {
		return errors.New(errors.Model, "categories is invalid", errors.Validation, nil)
	}
	if d.CreatedAt.After(d.UpdatedAt) {
		return errors.New(errors.Model, "createdAt cannot be after updatedAt", errors.Validation, nil)
	}

	// Validate all categories
	for _, category := range d.Categories {
		if err := category.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// ComputeTotals calculates and updates the total costs and savings fields
func (d *Dreamboard) ComputeTotals() {
	var totalCost, monthlySum monetary.Amount
	for _, dream := range d.Dreams {
		totalCost += dream.EstimatedCost
		monthlySum += dream.MonthlySavings
	}
	d.TotalDreamsCost = totalCost
	d.MonthlyNeeded = monthlySum
	d.RemainingAmount = d.SavedAmount - d.TotalDreamsCost
}

// CalculateSavedAmount calculates the saved amount by:
// 1. Summing all "dreams" category transactions from the financial sheet
// 2. Subtracting the estimated cost of realized dreams
func (d *Dreamboard) CalculateSavedAmount(financialSheet *financialsheet.Record) {
	var totalDreamMonths monetary.Amount
	var totalRealizedDreams monetary.Amount

	// Sum all "dreams" category transactions from financial sheet
	for _, yearData := range financialSheet.YearData {
		for _, monthData := range yearData {
			for _, category := range monthData.Categories {
				if category.Identifier == "dreams" {
					totalDreamMonths += category.Value
				}
			}
		}
	}

	// Sum estimated cost of realized dreams
	for _, dream := range d.Dreams {
		if dream.Completed {
			totalRealizedDreams += dream.EstimatedCost
		}
	}

	d.SavedAmount = totalDreamMonths - totalRealizedDreams
}

type Category struct {
	ObjectID   primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID         string             `json:"id,omitempty" bson:"-"`
	Identifier string             `json:"identifier" bson:"identifier"`
	Name       string             `json:"name" bson:"name"`
	Icon       string             `json:"icon" bson:"icon"`
	Color      string             `json:"color" bson:"color"`
}

const CategoryIconCDN = "https://images.dinbora.com.br/quadro-dos-sonhos"

// Predefined categories for backward compatibility
var (
	UndefinedCategory = Category{
		Identifier: "undefined",
		Name:       "Undefined",
		Icon:       "question-mark-outline",
		Color:      "#C9B3FF",
	}
	Professional = Category{
		Identifier: "professional",
		Name:       "Profissional",
		Icon:       CategoryIconCDN + "/profissional.png",
		Color:      "#BEFFE1",
	}
	Financial = Category{
		Identifier: "financial",
		Name:       "Financeiro",
		Icon:       CategoryIconCDN + "/financeiro.png",
		Color:      "#F6E4FF",
	}
	Leisure = Category{
		Identifier: "leisure",
		Name:       "Lazer",
		Icon:       CategoryIconCDN + "/lazer.png",
		Color:      "#A4FFE6",
	}
	Emotional = Category{
		Identifier: "emotional",
		Name:       "Emocional",
		Icon:       CategoryIconCDN + "/emocional.png",
		Color:      "#E4E9FF",
	}
	Intellectual = Category{
		Identifier: "intellectual",
		Name:       "Intelectual",
		Icon:       CategoryIconCDN + "/intelectual.png",
		Color:      "#F9FFFA",
	}
	Spiritual = Category{
		Identifier: "spiritual",
		Name:       "Espiritual",
		Icon:       CategoryIconCDN + "/espiritual.png",
		Color:      "#FFD9D9",
	}
	Physical = Category{
		Identifier: "physical",
		Name:       "Físico",
		Icon:       CategoryIconCDN + "/fisico.png",
		Color:      "#D0EAFF",
	}
	Intimate = Category{
		Identifier: "intimate",
		Name:       "Amoroso",
		Icon:       CategoryIconCDN + "/amoroso.png",
		Color:      "#F6E4FF",
	}
	Social = Category{
		Identifier: "social",
		Name:       "Social",
		Icon:       CategoryIconCDN + "/social.png",
		Color:      "#BEFFE1",
	}
	Familial = Category{
		Identifier: "familial",
		Name:       "Familiar",
		Icon:       CategoryIconCDN + "/familiar.png",
		Color:      "#F0EAFF",
	}
)

// IsValid validates all fields in the category
func (c Category) IsValid() bool {
	// Check if identifier is lowercase alphanumeric with no spaces
	for _, char := range c.Identifier {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9')) {
			return false
		}
	}

	// Check if color is a valid hex code
	if !strings.HasPrefix(c.Color, "#") || len(c.Color) != 7 {
		return false
	}
	for _, char := range c.Color[1:] {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return false
		}
	}

	return c.Identifier != "" && c.Name != "" && c.Icon != ""
}

func (c Category) Validate() error {
	if c.Identifier == "" {
		return errors.New(errors.Model, "identifier cannot be empty", errors.Validation, nil)
	}
	if c.Name == "" {
		return errors.New(errors.Model, "name cannot be empty", errors.Validation, nil)
	}
	if c.Icon == "" {
		return errors.New(errors.Model, "icon cannot be empty", errors.Validation, nil)
	}
	if c.Color == "" {
		return errors.New(errors.Model, "color cannot be empty", errors.Validation, nil)
	}

	// Validate identifier format
	for _, char := range c.Identifier {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9')) {
			return errors.New(errors.Model, "identifier must be lowercase alphanumeric with no spaces", errors.Validation, nil)
		}
	}

	// Validate color format
	if !strings.HasPrefix(c.Color, "#") || len(c.Color) != 7 {
		return errors.New(errors.Model, "color must be a valid hex code (e.g., #FF6347)", errors.Validation, nil)
	}
	for _, char := range c.Color[1:] {
		if !((char >= '0' && char <= '9') || (char >= 'A' && char <= 'F') || (char >= 'a' && char <= 'f')) {
			return errors.New(errors.Model, "color must be a valid hex code (e.g., #FF6347)", errors.Validation, nil)
		}
	}

	return nil
}

// String implements fmt.Stringer and returns the identifier
func (c Category) String() string {
	return c.Identifier
}

// StringValue returns the display name
func (c Category) StringValue() string {
	return c.Name
}

// MarshalJSON implements json.Marshaler
func (c Category) MarshalJSON() ([]byte, error) {
	return json.Marshal(struct {
		Identifier string `json:"identifier"`
		Name       string `json:"name"`
		Icon       string `json:"icon"`
		Color      string `json:"color"`
	}{
		Identifier: c.Identifier,
		Name:       c.Name,
		Icon:       c.Icon,
		Color:      c.Color,
	})
}

// UnmarshalJSON implements json.Unmarshaler
func (c *Category) UnmarshalJSON(data []byte) error {
	aux := struct {
		Identifier string `json:"identifier"`
		Name       string `json:"name"`
		Icon       string `json:"icon"`
		Color      string `json:"color"`
	}{}

	if err := json.Unmarshal(data, &aux); err != nil {
		return errors.New(errors.Model, "failed to unmarshal category", errors.Validation, err)
	}

	c.Identifier = aux.Identifier
	c.Name = aux.Name
	c.Icon = aux.Icon
	c.Color = aux.Color

	return c.Validate()
}

type Dream struct {
	ObjectID          primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                string             `json:"id,omitempty" bson:"-"`
	Category          CategoryIdentifier `json:"category" bson:"category"`
	Color             string             `json:"color" bson:"color"`
	Title             string             `json:"title" bson:"title"`
	TimeFrame         TimeFrame          `json:"timeFrame" bson:"timeFrame"`
	Deadline          time.Time          `json:"deadline" bson:"deadline"`
	EstimatedCost     monetary.Amount    `json:"estimatedCost" bson:"estimatedCost"`
	MonthlySavings    monetary.Amount    `json:"monthlySavings" bson:"monthlySavings"`
	MoneySource       []MoneySource      `json:"moneySource" bson:"moneySource"`
	Completed         bool               `json:"completed" bson:"completed"`
	CustomMoneySource string             `json:"customMoneySource,omitempty" bson:"customMoneySource,omitempty"`

	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`

	// New fields for shared dreams
	IsShared                 bool            `json:"isShared" bson:"isShared"`
	CreatorUserID            string          `json:"creatorUserId" bson:"creatorUserId"`
	CurrentRaisedAmount      monetary.Amount `json:"currentRaisedAmount" bson:"currentRaisedAmount"`
	FundingStatus            FundingStatus   `json:"fundingStatus" bson:"fundingStatus"`
	CalculatedDurationMonths *int            `json:"calculatedDurationMonths,omitempty" bson:"calculatedDurationMonths,omitempty"`
}

func (d *Dream) Validate() error {
	if !d.Category.IsValid() {
		return errors.New(errors.Model, "category identifier cannot be empty", errors.Validation, nil)
	}
	if len(d.Title) == 0 || len(d.Title) > 100 {
		return errors.New(errors.Model, "title must be between 1-100 characters", errors.Validation, nil)
	}
	if d.TimeFrame > Long {
		return errors.New(errors.Model, "invalid time frame", errors.Validation, nil)
	}
	if time.Until(d.Deadline) < 24*time.Hour {
		return errors.New(errors.Model, "deadline must be at least 24 hours in the future", errors.Validation, nil)
	}
	if d.EstimatedCost < 0 {
		return errors.New(errors.Model, "estimated cost cannot be negative", errors.Validation, nil)
	}
	if d.MonthlySavings < 0 || d.MonthlySavings > d.EstimatedCost {
		return errors.New(errors.Model, "monthly savings must be between 0 and estimated cost", errors.Validation, nil)
	}
	// if d.MoneySource == OtherMoneySource && d.CustomMoneySource == "" {
	// 	return errors.New(errors.Model, "custom money source required when selecting 'other'", errors.Validation, nil)
	// }
	for _, ms := range d.MoneySource {
		if ms < Salary || ms > MoneySourceOther {
			return errors.New(errors.Model, "invalid money source in list", errors.Validation, nil)
		}
	}

	// Validate shared dream fields
	if d.IsShared {
		if d.CreatorUserID == "" {
			return errors.New(errors.Model, "creator user ID must be specified for shared dreams", errors.Validation, nil)
		}
		if d.FundingStatus != "" && !d.FundingStatus.IsValid() {
			return errors.New(errors.Model, "invalid funding status", errors.Validation, nil)
		}
	}

	if d.CurrentRaisedAmount < 0 {
		return errors.New(errors.Model, "current raised amount cannot be negative", errors.Validation, nil)
	}

	return nil
}

type CategoryIdentifier byte

const (
	CategoryIdentifierUndefined CategoryIdentifier = iota
	CategoryIdentifierProfessional
	CategoryIdentifierFinancial
	CategoryIdentifierLeisure
	CategoryIdentifierEmotional
	CategoryIdentifierIntellectual
	CategoryIdentifierSpiritual
	CategoryIdentifierPhysical
	CategoryIdentifierIntimate
	CategoryIdentifierSocial
	CategoryIdentifierFamilial
)

func (ci CategoryIdentifier) IsValid() bool {
	return ci >= CategoryIdentifierProfessional && ci <= CategoryIdentifierFamilial
}

func (ci CategoryIdentifier) Validate() error {
	if !ci.IsValid() {
		return errors.New(errors.Model, "invalid category identifier value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ci CategoryIdentifier) String() string {
	return ci.StringValue()
}

// StringValue returns the string representation directly
func (ci CategoryIdentifier) StringValue() string {
	switch ci {
	case CategoryIdentifierProfessional:
		return "professional"
	case CategoryIdentifierFinancial:
		return "financial"
	case CategoryIdentifierLeisure:
		return "leisure"
	case CategoryIdentifierEmotional:
		return "emotional"
	case CategoryIdentifierIntellectual:
		return "intellectual"
	case CategoryIdentifierSpiritual:
		return "spiritual"
	case CategoryIdentifierPhysical:
		return "physical"
	case CategoryIdentifierIntimate:
		return "intimate"
	case CategoryIdentifierSocial:
		return "social"
	case CategoryIdentifierFamilial:
		return "familial"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (ci CategoryIdentifier) MarshalJSON() ([]byte, error) {
	return json.Marshal(ci.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (ci *CategoryIdentifier) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "professional":
		*ci = CategoryIdentifierProfessional
	case "financial":
		*ci = CategoryIdentifierFinancial
	case "leisure":
		*ci = CategoryIdentifierLeisure
	case "emotional":
		*ci = CategoryIdentifierEmotional
	case "intellectual":
		*ci = CategoryIdentifierIntellectual
	case "spiritual":
		*ci = CategoryIdentifierSpiritual
	case "physical":
		*ci = CategoryIdentifierPhysical
	case "intimate":
		*ci = CategoryIdentifierIntimate
	case "social":
		*ci = CategoryIdentifierSocial
	case "familial":
		*ci = CategoryIdentifierFamilial
	default:
		return errors.New(errors.Model, "invalid category identifier value", errors.Validation, nil)
	}
	return nil
}

type TimeFrame byte

const (
	UndefinedTimeFrame TimeFrame = iota
	Short
	Medium
	Long
)

// IsValid validates the time frame value
func (tf TimeFrame) IsValid() bool {
	return tf >= Short && tf <= Long
}

func (tf TimeFrame) Validate() error {
	if !tf.IsValid() {
		return errors.New(errors.Model, "invalid time frame value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (tf TimeFrame) String() string {
	return tf.StringValue()
}

// StringValue returns the string representation directly
func (tf TimeFrame) StringValue() string {
	switch tf {
	case Short:
		return "short"
	case Medium:
		return "medium"
	case Long:
		return "long"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (tf TimeFrame) MarshalJSON() ([]byte, error) {
	return json.Marshal(tf.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (tf *TimeFrame) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "short":
		*tf = Short
	case "medium":
		*tf = Medium
	case "long":
		*tf = Long
	default:
		return errors.New(errors.Model, "invalid time frame value", errors.Validation, nil)
	}
	return nil
}

type MoneySource byte

const (
	MoneySourceUndefined MoneySource = iota
	Salary
	Investments
	Freelance
	Business
	Inheritance
	Sale
	Savings
	MoneySourceOther
)

// IsValid validates the money source value
func (ms MoneySource) IsValid() bool {
	return ms >= Salary && ms <= MoneySourceOther
}

func (ms MoneySource) Validate() error {
	if !ms.IsValid() {
		return errors.New(errors.Model, "invalid money source value", errors.Validation, nil)
	}
	return nil
}

// String implements fmt.Stringer
func (ms MoneySource) String() string {
	return ms.StringValue()
}

// StringValue returns the string representation directly
func (ms MoneySource) StringValue() string {
	switch ms {
	case Salary:
		return "salary"
	case Investments:
		return "investments"
	case Freelance:
		return "freelance"
	case Business:
		return "business"
	case Inheritance:
		return "inheritance"
	case Sale:
		return "sale"
	case Savings:
		return "savings"
	case MoneySourceOther:
		return "other"
	default:
		return "undefined"
	}
}

// MarshalJSON implements json.Marshaler
func (ms MoneySource) MarshalJSON() ([]byte, error) {
	return json.Marshal(ms.StringValue())
}

// UnmarshalJSON implements json.Unmarshaler
func (ms *MoneySource) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	switch strings.ToLower(s) {
	case "salary":
		*ms = Salary
	case "investments":
		*ms = Investments
	case "freelance":
		*ms = Freelance
	case "business":
		*ms = Business
	case "inheritance":
		*ms = Inheritance
	case "sale":
		*ms = Sale
	case "savings":
		*ms = Savings
	case "other":
		*ms = MoneySourceOther
	default:
		return errors.New(errors.Model, "invalid money source value", errors.Validation, nil)
	}
	return nil
}

// FundingStatus represents the status of a dream's funding
type FundingStatus string

const (
	FundingStatusPersonalActive            FundingStatus = "PERSONAL_ACTIVE"
	FundingStatusSharedOpenForParticipants FundingStatus = "SHARED_OPEN_FOR_PARTICIPANTS"
	FundingStatusSharedActiveCollecting    FundingStatus = "SHARED_ACTIVE_COLLECTING"
	FundingStatusFullyFunded               FundingStatus = "FULLY_FUNDED"
	FundingStatusCompleted                 FundingStatus = "COMPLETED"
	FundingStatusCancelled                 FundingStatus = "CANCELLED"
)

// IsValid validates the funding status value
func (fs FundingStatus) IsValid() bool {
	switch fs {
	case FundingStatusPersonalActive, FundingStatusSharedOpenForParticipants,
		FundingStatusSharedActiveCollecting, FundingStatusFullyFunded,
		FundingStatusCompleted, FundingStatusCancelled:
		return true
	default:
		return false
	}
}

// ShareLink represents the mechanism for inviting users to a shared dream
type ShareLink struct {
	ObjectID  primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID        string             `json:"id,omitempty" bson:"-"`
	DreamID   string             `json:"dreamId" bson:"dreamId"`
	Token     string             `json:"token" bson:"token"`
	IsEnabled bool               `json:"isEnabled" bson:"isEnabled"`
	ExpiresAt time.Time          `json:"expiresAt" bson:"expiresAt"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Validate validates the ShareLink
func (sl *ShareLink) Validate() error {
	if sl.DreamID == "" {
		return errors.New(errors.Model, "dream ID must be specified", errors.Validation, nil)
	}
	if sl.Token == "" {
		return errors.New(errors.Model, "token must be specified", errors.Validation, nil)
	}
	if sl.ExpiresAt.Before(time.Now()) {
		return errors.New(errors.Model, "expiration date must be in the future", errors.Validation, nil)
	}
	return nil
}

// ContributionStatus represents the status of a user's contribution to a shared dream
type ContributionStatus string

const (
	ContributionStatusActive              ContributionStatus = "ACTIVE"
	ContributionStatusCancelledByUser     ContributionStatus = "CANCELLED_BY_USER"
	ContributionStatusEndedDreamCancelled ContributionStatus = "ENDED_DREAM_CANCELLED"
)

// IsValid validates the contribution status value
func (cs ContributionStatus) IsValid() bool {
	switch cs {
	case ContributionStatusActive, ContributionStatusCancelledByUser, ContributionStatusEndedDreamCancelled:
		return true
	default:
		return false
	}
}

// Contribution represents a user's participation in a shared dream
type Contribution struct {
	ObjectID             primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID                   string             `json:"id,omitempty" bson:"-"`
	DreamID              string             `json:"dreamId" bson:"dreamId"`
	ContributorUserID    string             `json:"contributorUserId" bson:"contributorUserId"`
	IsCreator            bool               `json:"isCreator" bson:"isCreator"`
	MonthlyPledgedAmount monetary.Amount    `json:"monthlyPledgedAmount" bson:"monthlyPledgedAmount"`
	Status               ContributionStatus `json:"status" bson:"status"`
	JoinedAt             time.Time          `json:"joinedAt" bson:"joinedAt"`
	UpdatedAt            time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// Validate validates the Contribution
func (c *Contribution) Validate() error {
	if c.DreamID == "" {
		return errors.New(errors.Model, "dream ID must be specified", errors.Validation, nil)
	}
	if c.ContributorUserID == "" {
		return errors.New(errors.Model, "contributor user ID must be specified", errors.Validation, nil)
	}
	if c.MonthlyPledgedAmount < 0 {
		return errors.New(errors.Model, "monthly pledged amount cannot be negative", errors.Validation, nil)
	}
	if !c.Status.IsValid() {
		return errors.New(errors.Model, "invalid contribution status", errors.Validation, nil)
	}
	return nil
}

// DeletedDreamboard represents a soft-deleted dreamboard entry
type DeletedDreamboard struct {
	Dreamboard  Dreamboard    `json:"dreamboard" bson:"dreamboard"`
	DeletedBy   string        `json:"deletedBy" bson:"deletedBy"`
	Reason      string        `json:"reason" bson:"reason"`
	DeletedAt   time.Time     `json:"deletedAt" bson:"deletedAt"`
	ScheduledAt time.Time     `json:"scheduledAt,omitempty" bson:"scheduledAt,omitempty"`
	RestoredAt  *time.Time    `json:"restoredAt,omitempty" bson:"restoredAt,omitempty"`
	ExpiresAt   time.Time     `json:"expiresAt" bson:"expiresAt"`
	Metadata    []interface{} `json:"metadata" bson:"metadata"`
}
