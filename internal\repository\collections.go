package repository

const (
	USERS_COLLECTION                       = "users"                       // 000000000000000000000001
	USERS_COLLECTION_TRASH                 = "users.trash"                 // 000000000000000000000001.trash
	PRODUCTS_COLLECTION                    = "products"                    // 000000000000000000000002
	TRAILS_COLLECTION                      = "trails"                      // 000000000000000000000003
	TRAILS_EXTRA_COLLECTION                = "trails.extra"                // Not in legacy
	TRAILS_TUTORIAL_COLLECTION             = "trails.tutorial"             // 000000000000000000000012
	PROGRESSIONS_COLLECTION                = "progressions"                // 000000000000000000000004
	TROPHIES_COLLECTION                    = "trophies"                    // 000000000000000000000005
	VAULTS_COLLECTION                      = "vaults"                      // 000000000000000000000006
	CONTRACTS_COLLECTION                   = "contracts"                   // 000000000000000000000007
	TICKERS_COLLECTION                     = "tickers"                     // 000000000000000000000008
	INVESTMENT_CATEGORIES_COLLECTION       = "investment_categories"       // 000000000000000000000009
	WALLETS_COLLECTION                     = "wallets"                     // 000000000000000000000010
	INVOICES_COLLECTION                    = "invoices"                    // 000000000000000000000011
	DREAMBOARDS_COLLECTION                 = "dreamboards"                 // 000000000000000000000013
	DREAMBOARDS_COLLECTION_TRASH           = "dreamboards.trash"           // 000000000000000000000013.trash
	FINANCIAL_SHEETS_COLLECTION            = "financial_sheets"            // 000000000000000000000014
	FINANCIAL_SHEETS_COLLECTION_TRASH      = "financial_sheets.trash"      // 000000000000000000000014.trash
	FINANCIAL_SHEETS_COLLECTION_CATEGORIES = "financial_sheets.categories" // 000000000000000000000014.categories
	FINANCIAL_DNA_COLLECTION               = "financial_dna"               // 000000000000000000000015
	FINANCIAL_DNA_COLLECTION_TRASH         = "financial_dna.trash"         // 000000000000000000000015.trash
	LEAGUES_COLLECTION                     = "leagues"                     // 000000000000000000000016
	WEBHOOKS_COLLECTION                    = "webhooks"                    // f00000000000000000000000
	CHANGELOGS_COLLECTION                  = "changelogs"                  // f0000000000000000000000c
)
