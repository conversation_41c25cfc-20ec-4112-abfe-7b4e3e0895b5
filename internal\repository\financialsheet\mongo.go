package financialsheet

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
	trash      *mongo.Collection

	categoryCollection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.FINANCIAL_SHEETS_COLLECTION),
		trash:      db.Collection(repository.FINANCIAL_SHEETS_COLLECTION_TRASH),

		categoryCollection: db.Collection(repository.FINANCIAL_SHEETS_COLLECTION_CATEGORIES),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userID", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on financial sheet.user field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

// CRUD
func (m mongoDB) Create(ctx context.Context, record *financialsheet.Record) (string, error) {
	insertedResult, err := m.collection.InsertOne(ctx, record)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.FinancialSheetConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.FinancialSheetCreateFailed, errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*financialsheet.Record, error) {
	var record financialsheet.Record
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&record)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindFailed, errors.Internal, err)
	}

	if record.ObjectID.IsZero() {
		return nil, errors.New(errors.Repository, errors.FinancialSheetInvalidID, errors.BadRequest, nil)
	}

	record.ID = record.ObjectID.Hex()
	return &record, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*financialsheet.Record, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*financialsheet.Record
	if err = cursor.All(ctx, &records); err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindAllFailed, errors.Internal, err)
	}

	// Set ID field from ObjectID for each record
	for _, record := range records {
		record.ID = record.ObjectID.Hex()
	}

	return records, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	var record financialsheet.Record
	err := m.collection.FindOne(ctx, bson.D{{Key: "userID", Value: userID}}).Decode(&record)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetUserNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetFindByUserFailed, errors.Internal, err)
	}
	record.ID = record.ObjectID.Hex()
	return &record, nil
}

func (m mongoDB) Update(ctx context.Context, record *financialsheet.Record) error {
	if record.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.FinancialSheetInvalidID, errors.BadRequest, nil)
	}

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: record.ObjectID}},
		bson.D{{Key: "$set", Value: record}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.FinancialSheetConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.FinancialSheetUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.FinancialSheetDeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetNotFound, errors.NotFound, nil)
	}
	return nil
}

// Category CRUD
func (m mongoDB) CreateCategory(ctx context.Context, category *financialsheet.Category) (string, error) {
	insertedResult, err := m.categoryCollection.InsertOne(ctx, category)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, errors.FinancialSheetCategoryConflictExists, errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, errors.FinancialSheetCategoryCreateFailed, errors.Internal, err)
	}
	category.ID = insertedResult.InsertedID.(primitive.ObjectID).Hex()
	return category.ID, nil
}

func (m mongoDB) FindCategory(ctx context.Context, id primitive.ObjectID) (*financialsheet.Category, error) {
	var category financialsheet.Category
	err := m.categoryCollection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}

	if category.ObjectID.IsZero() {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryInvalidID, errors.BadRequest, nil)
	}

	category.ID = category.ObjectID.Hex()
	return &category, nil
}

func (m mongoDB) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	cursor, err := m.categoryCollection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var categories []*financialsheet.Category
	if err = cursor.All(ctx, &categories); err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}

	// Set ID field from ObjectID for each category
	for _, category := range categories {
		category.ID = category.ObjectID.Hex()
	}

	return categories, nil
}

func (m mongoDB) FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	var category financialsheet.Category
	err := m.categoryCollection.FindOne(ctx, bson.D{{Key: "identifier", Value: identifier}}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, errors.FinancialSheetCategoryFindFailed, errors.Internal, err)
	}
	category.ID = category.ObjectID.Hex()
	return &category, nil
}

func (m mongoDB) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	if category.ObjectID.IsZero() {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryInvalidID, errors.BadRequest, nil)
	}

	opts := options.Update().SetUpsert(false)
	result, err := m.categoryCollection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: category.ObjectID}},
		bson.D{{Key: "$set", Value: category}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, errors.FinancialSheetCategoryConflictUpdate, errors.Conflict, err)
		}
		return errors.New(errors.Repository, errors.FinancialSheetCategoryUpdateFailed, errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteCategory(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.categoryCollection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryDeleteFailed, errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, errors.FinancialSheetCategoryNotFound, errors.NotFound, nil)
	}
	return nil
}

// Transactions CRUD
// FindAllTransactions implements Repository.FindAllTransactions
func (m mongoDB) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType) ([]*financialsheet.Transaction, error) {
	record, err := m.FindByUser(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Repository, errors.FinancialSheetUserNotFound, errors.NotFound, err)
	}

	var transactions []*financialsheet.Transaction
	for _, yearData := range record.YearData {
		for _, monthData := range yearData {
			for i := range monthData.Transactions {
				transaction := monthData.Transactions[i]
				if categoryType == "" || transaction.Type == categoryType {
					transaction.ID = transaction.ObjectID.Hex()
					transactions = append(transactions, &transaction)
				}
			}
		}
	}

	return transactions, nil
}
