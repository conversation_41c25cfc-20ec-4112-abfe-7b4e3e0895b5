package contribution

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.Contribution, error)
	FindByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	FindByUserID(ctx context.Context, userID string) ([]*model.Contribution, error)
	FindByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error)
	FindActiveByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, contribution *model.Contribution) (string, error)

	// Update operations
	Update(ctx context.Context, contribution *model.Contribution) error
	UpdateStatusByDreamID(ctx context.Context, dreamID string, status model.ContributionStatus) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
