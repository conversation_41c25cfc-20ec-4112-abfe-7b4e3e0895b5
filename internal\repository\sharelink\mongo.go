package sharelink

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.SHARELINKS_COLLECTION),
	}

	// Create unique index on token field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "token", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on sharelink.token field")
	}

	// Create index on dreamId field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreamId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on sharelink.dreamId field")
	}

	// Create TTL index on expiresAt field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "expiresAt", Value: 1}},
			Options: options.Index().SetExpireAfterSeconds(0),
		},
	)
	if err != nil {
		log.Println("warning: failed to create TTL index on sharelink.expiresAt field")
	}

	return repo
}

// Create creates a new share link
func (m mongoDB) Create(ctx context.Context, shareLink *model.ShareLink) (string, error) {
	shareLink.CreatedAt = time.Now()
	shareLink.UpdatedAt = shareLink.CreatedAt

	insertedResult, err := m.collection.InsertOne(ctx, shareLink)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, "share link token already exists", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "failed to create share link", errors.Internal, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

// Find finds a share link by ID
func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link", errors.Internal, err)
	}

	shareLink.ID = shareLink.ObjectID.Hex()
	return &shareLink, nil
}

// FindByToken finds a share link by token
func (m mongoDB) FindByToken(ctx context.Context, token string) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.collection.FindOne(ctx, bson.D{{Key: "token", Value: token}}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link by token", errors.Internal, err)
	}

	shareLink.ID = shareLink.ObjectID.Hex()
	return &shareLink, nil
}

// FindByDreamID finds a share link by dream ID
func (m mongoDB) FindByDreamID(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.collection.FindOne(ctx, bson.D{{Key: "dreamId", Value: dreamID}}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link by dream ID", errors.Internal, err)
	}

	shareLink.ID = shareLink.ObjectID.Hex()
	return &shareLink, nil
}

// Update updates a share link
func (m mongoDB) Update(ctx context.Context, shareLink *model.ShareLink) error {
	if shareLink.ObjectID.IsZero() {
		return errors.New(errors.Repository, "invalid share link ID", errors.BadRequest, nil)
	}

	shareLink.UpdatedAt = time.Now()

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: shareLink.ObjectID}},
		bson.D{{Key: "$set", Value: shareLink}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "share link token already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update share link", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "share link not found", errors.NotFound, nil)
	}
	return nil
}

// Delete deletes a share link
func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete share link", errors.Internal, err)
	}
	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "share link not found", errors.NotFound, nil)
	}
	return nil
}
