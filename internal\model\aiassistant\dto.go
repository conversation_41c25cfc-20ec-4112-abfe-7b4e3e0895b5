package aiassistant

// AIContextDTO is the main DTO for AI context aggregation
type AIContextDTO struct {
	UserID                 string                    `json:"user_id"`
	ProfileSummary         ProfileSummaryDTO         `json:"profile_summary,omitempty"`
	DSOPProgress           DSOPProgressDTO           `json:"dsop_progress,omitempty"`
	ActiveDreams           []DreamForAIDTO           `json:"active_dreams,omitempty"`
	FinancialDNASnapshot   FinancialDNASnapshotDTO   `json:"financial_dna_snapshot,omitempty"`
	FinancialHabitsSummary FinancialHabitsSummaryDTO `json:"financial_habits_summary,omitempty"`
	SimulationHighlights   []SimulationHighlightDTO  `json:"simulation_highlights,omitempty"`
	DataCompleteness       map[string]bool           `json:"data_completeness_flags,omitempty"`
}

// ProfileSummaryDTO contains basic user profile information
type ProfileSummaryDTO struct {
	Name               string   `json:"name"`
	Email              string   `json:"email"`
	AgeRange           string   `json:"age_range,omitempty"`
	FinancialSituation string   `json:"financial_situation,omitempty"`
	FinancialGoal      string   `json:"financial_goal,omitempty"`
	PersonalInterests  []string `json:"personal_interests,omitempty"`
	OnboardingComplete bool     `json:"onboarding_complete"`
}

// DSOPProgressDTO contains educational progress information
type DSOPProgressDTO struct {
	CompletedLessons     int      `json:"completed_lessons"`
	TotalLessons         int      `json:"total_lessons"`
	CompletedChallenges  int      `json:"completed_challenges"`
	TotalChallenges      int      `json:"total_challenges"`
	CurrentTrails        []string `json:"current_trails,omitempty"`
	CompletedTrails      []string `json:"completed_trails,omitempty"`
	EarnedTrophies       int      `json:"earned_trophies"`
	TotalTrophies        int      `json:"total_trophies"`
	RecentLessonTopics   []string `json:"recent_lesson_topics,omitempty"`
	RecentChallengeTypes []string `json:"recent_challenge_types,omitempty"`
}

// DreamForAIDTO contains dream information optimized for AI consumption
type DreamForAIDTO struct {
	Name            string  `json:"name"`
	Category        string  `json:"category"`
	TargetAmount    int64   `json:"target_amount"`
	SavedAmount     int64   `json:"saved_amount"`
	PercentSaved    float64 `json:"percent_saved"`
	MonthlyNeeded   int64   `json:"monthly_needed"`
	TargetDate      string  `json:"target_date,omitempty"`
	MonthsRemaining int     `json:"months_remaining,omitempty"`
	Priority        int     `json:"priority,omitempty"`
}

// FinancialDNASnapshotDTO contains financial DNA information
type FinancialDNASnapshotDTO struct {
	TreeProgress          float64                  `json:"tree_progress"`
	FinancialDistribution map[string]float64       `json:"financial_distribution,omitempty"`
	BreakCycles           []string                 `json:"break_cycles,omitempty"`
	KeyMembers            []FamilyMemberSummaryDTO `json:"key_members,omitempty"`
}

// FamilyMemberSummaryDTO contains family member information
type FamilyMemberSummaryDTO struct {
	Name            string `json:"name"`
	Relationship    string `json:"relationship"`
	FinancialStatus string `json:"financial_status"`
}

// FinancialHabitsSummaryDTO contains financial habits information
type FinancialHabitsSummaryDTO struct {
	MonthlyIncome        int64                   `json:"monthly_income"`
	MonthlyCostsOfLiving int64                   `json:"monthly_costs_of_living"`
	MonthlyExpenses      int64                   `json:"monthly_expenses"`
	MonthlyBalance       int64                   `json:"monthly_balance"`
	SavingsRate          float64                 `json:"savings_rate"`
	TopIncomeCategories  []CategorySummaryDTO    `json:"top_income_categories,omitempty"`
	TopExpenseCategories []CategorySummaryDTO    `json:"top_expense_categories,omitempty"`
	RecentTransactions   []TransactionSummaryDTO `json:"recent_transactions,omitempty"`
	LeagueStatus         string                  `json:"league_status,omitempty"`
	ConsistencyScore     float64                 `json:"consistency_score,omitempty"`
}

// CategorySummaryDTO contains category summary information
type CategorySummaryDTO struct {
	Name    string  `json:"name"`
	Amount  int64   `json:"amount"`
	Percent float64 `json:"percent"`
}

// TransactionSummaryDTO contains transaction summary information
type TransactionSummaryDTO struct {
	Description string `json:"description"`
	Category    string `json:"category"`
	Amount      int64  `json:"amount"`
	Date        string `json:"date"`
	Type        string `json:"type"` // "income", "expense", "cost_of_living"
}

// SimulationHighlightDTO contains simulation highlight information
type SimulationHighlightDTO struct {
	Name           string `json:"name"`
	Description    string `json:"description"`
	CurrentValue   int64  `json:"current_value"`
	ProjectedValue int64  `json:"projected_value"`
	TimeFrame      string `json:"time_frame"`
}
